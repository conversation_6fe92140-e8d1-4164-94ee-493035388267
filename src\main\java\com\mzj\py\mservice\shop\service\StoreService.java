package com.mzj.py.mservice.shop.service;

import cn.hutool.core.collection.CollUtil;
import com.mzj.py.aop.StorePermission;
import com.mzj.py.commons.DateUtils;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.enums.StoreReviewEnum;
import com.mzj.py.commons.enums.StoreTypeEnum;
import com.mzj.py.commons.enums.StoreUserTypeEnum;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.common.PageResult;
import com.mzj.py.mservice.home.entity.Device;
import com.mzj.py.mservice.home.repository.DeviceRepository;
import com.mzj.py.mservice.shop.entity.Shop;
import com.mzj.py.mservice.shop.entity.ShopUserRef;
import com.mzj.py.mservice.shop.repository.ShopRepository;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import com.mzj.py.mservice.shop.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class StoreService {

    @Resource
    private JdbcTemplate jdbcTemplate;
    @Resource
    private ShopRepository shopRepository;
    @Resource
    private ShopUserRefRepository shopUserRefRepository;
    @Resource
    private DeviceRepository deviceRepository;

    public ResultBean<PageResult<StorePageVo>> pageList(StorePageParams pageParams) {
        List<String> query = new ArrayList<>();
        List<Object> args = new ArrayList<>();
        if (StringUtils.isNotBlank(pageParams.getKeyword())) {
            query.add(" shop.shop_name like ? ");
            args.add("%" + pageParams.getKeyword() + "%");
        }
        if (pageParams.getStoreType() != null) {
            query.add(" shop.type = ? ");
            args.add(pageParams.getStoreType());
        }
        if (pageParams.getStoreId() != null) {
            query.add(" (shop.id = ? OR shop.parent_id = ?) ");
            args.add(pageParams.getStoreId());
            args.add(pageParams.getStoreId());
        }

        StringBuilder where = new StringBuilder();
        if (CollUtil.isNotEmpty(query)) {
            where.append(" WHERE ");
            where.append(String.join(" AND ", query));
        }

        String sql = "SELECT " +
                " shop.id, " +
                " shop.create_time, " +
                " shop.shop_name as storeName, " +
                " shop.type as storeType, " +
                " shop.address, " +
                " shop.parent_id, " +
                " parentShop.shop_name as parentName, " +
                " GROUP_CONCAT(itemShop.shop_name, ',') as itemNames, " +
                " shop.status, " +
                " ref2.user_id, " +
                " u.nickname, " +
                " COUNT(DISTINCT ref.user_id ) AS merchantCount, " +
                " COUNT(DISTINCT device.id ) AS deviceCount  " +
                "FROM dub_shop shop " +
                "LEFT JOIN dub_user_shop_ref ref2 ON ref2.shop_id = shop.id AND ref2.role =  "
                + StoreUserTypeEnum.ADMIN.getCode() +
                " LEFT JOIN dub_wechat_user u ON u.id = ref2.user_id " +
                "LEFT JOIN dub_shop parentShop ON parentShop.id = shop.parent_id " +
                "LEFT JOIN dub_shop itemShop ON itemShop.parent_id = shop.id " +
                "LEFT JOIN dub_user_shop_ref ref ON ref.shop_id = shop.id " +
                "LEFT JOIN dub_device device ON device.shop_id = shop.id AND device.bind_status = 1 AND device.del_status = 0";

        where.append(" GROUP BY shop.id, ref2.user_id");
        Long count = jdbcTemplate.queryForObject("select count(1) from(" + sql + where + ")t ", Long.class,
                args.toArray());
        if (count == null || 0 == count.intValue()) {
            return ResultBean.successfulResult(PageResult.empty());
        }
        where.append(" ORDER BY shop.create_time DESC ");
        where.append(" LIMIT ? OFFSET ? ");
        args.add(pageParams.getPageSize());
        args.add((pageParams.getCurrentPage() - 1) * pageParams.getPageSize());
        List<StorePageVo> pageResVos = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(StorePageVo.class),
                args.toArray());
        return ResultBean.successfulResult(PageResult.of(pageResVos, count));

    }

    /**
     * 2.5.2 新增门店
     * 1、数据保存在 dub_shop 表，一级店铺的 parent_id 为 0
     * 2、新增门店的门店类型默认都是总店
     * 3、门店被绑定后，门店类型变为分店
     *
     * @param addVo
     * @return
     */
    public ResultBean<String> add(StoreAddVo addVo) {
        // 如果用户已有绑定关系，则不允许新增门店
        List<ShopUserRef> refsList = jdbcTemplate.query(
                String.format("SELECT * FROM dub_user_shop_ref WHERE user_id = %s ", addVo.getCUserId()),
                new BeanPropertyRowMapper<>(ShopUserRef.class));
        if (CollUtil.isNotEmpty(refsList)) {
            return ResultBean.failedResultWithMsg("该用户已有绑定关系，不允许新增门店");
        }
        if (addVo.getParentId() > 0 && !shopRepository.existsById(addVo.getParentId())) {
            return ResultBean.failedResultWithMsg("上级店铺不存在");
        }
        Shop shop = new Shop();
        shop.setShopName(addVo.getStoreName());
        shop.setCreateUserId(addVo.getCUserId());
        shop.setParentId(addVo.getParentId());
        shop.setCreateTime(new Date());
        if (addVo.getParentId() > 0) {
            shop.setType(StoreTypeEnum.BRANCH.getCode());
            shop.setStatus(StoreReviewEnum.WAIT.ordinal());
        } else {
            shop.setType(StoreTypeEnum.PARENT.getCode());
            shop.setStatus(StoreReviewEnum.PASS.ordinal());
        }
        shopRepository.save(shop);
        ShopUserRef ref = new ShopUserRef();
        ref.setShopId(shop.getId());
        ref.setUserId(addVo.getCUserId());
        ref.setRole(StoreUserTypeEnum.ADMIN.getCode());
        shopUserRefRepository.save(ref);
        return ResultBean.successfulResultWithMsg("新增成功", null);
    }

    /**
     * {"id":0,"storeName":"","address":"","contactPerson":"","contactPhone":""}
     *
     * @param addVo
     * @return
     */
    @StorePermission(operate = true, key = "id")
    public ResultBean<String> update(StoreAddVo addVo) {
        // 先通过 Optional 安全获取店铺信息，避免直接 get() 导致 NoSuchElementException
        Optional<Shop> optionalShop = shopRepository.findById(addVo.getId());
        if (!optionalShop.isPresent()) {
            return ResultBean.failedResultWithMsg("店铺不存在");
        }
        Shop shop = optionalShop.get();
        if (addVo.getParentId() > 0 && !shopRepository.existsById(addVo.getParentId())) {
            return ResultBean.failedResultWithMsg("上级店铺不存在");
        }
        if (StringUtils.isNotEmpty(addVo.getStoreName())) {
            shop.setShopName(addVo.getStoreName());
        }
        if (StringUtils.isNotEmpty(addVo.getAddress())) {
            shop.setAddress(addVo.getAddress());
        }
        if (addVo.getParentId() > 0) {
            List<Shop> childShops = shopRepository.findByParentId(addVo.getId());
            if (CollUtil.isNotEmpty(childShops)) {
                return ResultBean.failedResultWithMsg("总店已绑定分店，请先解除绑定关系");
            }
            if (!Objects.equals(shop.getParentId(), addVo.getParentId())) {
                shop.setType(StoreTypeEnum.BRANCH.getCode());
                shop.setStatus(StoreReviewEnum.WAIT.ordinal());
            }
        }
        shop.setParentId(addVo.getParentId());
        shopRepository.save(shop);
        return ResultBean.successfulResultWithMsg("修改成功", null);
    }

    /**
     * 2.5.4 删除
     * 1、需要列表选择一条或者多条数据
     * 2、总店已经绑定分店，不能删除
     * 3、只有店的时候，可以删除，在删除总店数据时，需要删除以下表数据
     * a) dub_user_shop_ref，删除门店与用户的关系
     * b) dub_device，删除对应门店的设备
     * c) dub_shop，总店的数据，根据 ID 删除
     * 4、其他表的数据不删除，保留
     *
     * @param id
     * @return
     */
    @StorePermission(operate = true, key = "id")
    public ResultBean<String> delete(Long id) {
        Shop shop = shopRepository.getOne(id);
        if (shop.getId() == null) {
            return ResultBean.failedResultWithMsg("店铺不存在");
        }
        List<Shop> cls = jdbcTemplate.query("select * from dub_shop where parent_id = ?",
                new BeanPropertyRowMapper<>(Shop.class), id);
        if (CollUtil.isNotEmpty(cls)) {
            return ResultBean.failedResultWithMsg("该店铺有子店铺，不能删除");
        }
        shopRepository.deleteById(id);
        jdbcTemplate.update("delete from dub_user_shop_ref where shop_id = ?", id);
        jdbcTemplate.update("delete from dub_device where shop_id = ?", id);
        return ResultBean.successfulResultWithMsg("删除成功", null);
    }

    /**
     * 门店绑定设备
     *
     * @param bindParams
     * @return
     */
    @StorePermission(key = "storeId")
    public ResultBean<String> bindDevice(StoreBindDeviceParams bindParams) {
        List<Device> devs = deviceRepository.findAllById(bindParams.getDeviceIds());
        if (CollUtil.isEmpty(devs)) {
            return ResultBean.failedResultWithMsg("设备不存在");
        }
        ShopUserRef ref = shopUserRefRepository.findByShopIdAndRole(bindParams.getStoreId(),
                StoreUserTypeEnum.ADMIN.getCode());
        if (ref == null) {
            return ResultBean.failedResultWithMsg("门店未绑定管理员");
        }
        devs = devs.stream().peek(device -> {
            if (device.getShopId() != null) {
                throw new CustomException("设备已绑定门店,请联系客服解绑");
            }
            device.setShopId(bindParams.getStoreId());
            device.setUserId(ref.getUserId());
            device.setBindStatus(1);
        }).collect(Collectors.toList());
        deviceRepository.saveAll(devs);
        return ResultBean.successfulResult("绑定设备成功");
    }

    /**
     * 获取绑定用户列表
     *
     * @param storeId
     * @param pageParams
     * @return
     */
    @StorePermission(key = "storeId")
    public ResultBean<PageResult<StoreUserPageVo>> getUsers(Long storeId, StorePageParams pageParams) {
        // 使用参数占位符避免 SQL 注入，并且统计数量时无需关联用户表
        String baseSql = "SELECT u.id, u.nickname AS username, u.phone, u.auth_time AS createTime, ref.role " +
                "FROM dub_user_shop_ref ref " +
                "LEFT JOIN dub_wechat_user u ON ref.user_id = u.id " +
                "WHERE ref.shop_id = ?";

        // 统计总数，无需关联 dub_wechat_user 表
        Long count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM dub_user_shop_ref WHERE shop_id = ?", Long.class,
                storeId);
        if (count == null || count == 0) {
            return ResultBean.successfulResult(PageResult.empty());
        }

        int limit = pageParams.getPageSize();
        int offset = (pageParams.getCurrentPage() - 1) * pageParams.getPageSize();
        String pageSql = baseSql + " LIMIT ? OFFSET ? ";

        log.info("查询店铺用户列表，pageSql:{}, storeId:{}, limit:{}, offset:{}", pageSql, storeId, limit, offset);
        List<StoreUserPageVo> pageResVos = jdbcTemplate.query(pageSql,
                new BeanPropertyRowMapper<>(StoreUserPageVo.class), storeId, limit, offset);
        return ResultBean.successfulResult(PageResult.of(pageResVos, count));
    }

    /**
     * 2.5.5 店铺详情
     *
     * @param id
     * @return
     */
    public ResultBean<StorePageVo> detail(Long id, Long userId) {
        ShopUserRef shopUserRef = shopUserRefRepository.findByShopIdAndUserId(id, userId);
        if (shopUserRef == null) {
            return ResultBean.failedResultWithMsg("用户未绑定该店铺");
        }
        StorePageParams pageParams = new StorePageParams();
        pageParams.setStoreId(id);
        pageParams.setCurrentPage(1);
        pageParams.setPageSize(10);
        ResultBean<PageResult<StorePageVo>> resultBean = pageList(pageParams);
        if (resultBean.isOk()) {
            PageResult<StorePageVo> res = resultBean.getResultData();
            if (res != null && CollUtil.isNotEmpty(res.getResult())) {
                return ResultBean.successfulResult(res.getResult().get(0));
            }
        }
        return ResultBean.failedResultWithMsg("店铺不存在");
    }

    /**
     * 2.5.6 绑定用户
     *
     * @param bindUserParams
     * @return
     */
    @StorePermission(key = "storeId")
    public ResultBean<String> bindUser(StoreBindUserParams bindUserParams, Long userId) {
        // 查询用户绑定信息
        List<ShopUserRef> refs = shopUserRefRepository.queryByUserId(bindUserParams.getUserId());
        log.info("查询用户绑定信息，refs:{}", refs);
        if (CollUtil.isNotEmpty(refs)) {
            return ResultBean.failedResultWithMsg("用户已有绑定，不能再绑定");
        }
        ShopUserRef ref = new ShopUserRef();
        ref.setShopId(bindUserParams.getStoreId());
        ref.setUserId(bindUserParams.getUserId());
        ref.setRole(StoreUserTypeEnum.USER.getCode());
        shopUserRefRepository.save(ref);
        return ResultBean.successfulResultWithMsg("绑定用户成功", null);
    }

    @StorePermission(operate = true, key = "storeId")
    public ResultBean<PageResult<StoreUserPageVo>> unbindUser(StoreBindUserParams bindUserParams) {
        jdbcTemplate.update("delete from dub_user_shop_ref where shop_id = ? and user_id = ?",
                bindUserParams.getStoreId(), bindUserParams.getUserId());
        return ResultBean.successfulResultWithMsg("解绑用户成功", null);
    }

    public ResultBean<List<Shop>> getParentShopByPhone(StoreParentPageParams storeParentPageParams) {
        String sql;
        List<Shop> shops = new ArrayList<>();
        if (StringUtils.isNotEmpty(storeParentPageParams.getPhone())) {
            sql = "select * from dub_shop " +
                    "where type = 1 and parent_id = 0 " +
                    "and id in (select shop_id from dub_user_shop_ref where user_id in (select id from dub_wechat_user where phone = ?))" +
                    " ORDER BY create_time DESC LIMIT ? OFFSET ?";
            shops = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(Shop.class),
                    storeParentPageParams.getPhone(),
                    storeParentPageParams.getPageSize(),
                    (storeParentPageParams.getCurrentPage() - 1) * storeParentPageParams.getPageSize());
        } else {
            sql = "select * from dub_shop where type = 1 and parent_id = 0 ORDER BY create_time DESC LIMIT ? OFFSET ?";
            shops = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(Shop.class),
                    storeParentPageParams.getPageSize(),
                    (storeParentPageParams.getCurrentPage() - 1) * storeParentPageParams.getPageSize());
        }
        if (CollUtil.isNotEmpty(shops)) {
            return ResultBean.successfulResult(shops);
        } else {
            return ResultBean.failedResultWithMsg("未找到该手机号绑定的总店");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ShopUserRef createShop(Long userId) {
        Shop shop = new Shop();
        shop.setShopName("门店【".concat(DateUtils.datetimeToString(new Date(), "yyyyMMddss")).concat("】"));
        shop.setParentId(0L);
        shop.setType(1);
        shop.setStatus(1);
        shop.setCreateTime(new Date());
        shop.setCreateUserId(userId);
        shop = shopRepository.save(shop);

        ShopUserRef ref = new ShopUserRef();
        ref.setShopId(shop.getId());
        ref.setUserId(userId);
        ref.setRole(1);
        shopUserRefRepository.save(ref);
        return ref;
    }

    /**
     * 微信用户只能 查询 自己
     *
     * @param shopId
     * @return
     */
    public List<ShopUserVo> shopUserList(Long shopId) {
        String sql = "SELECT wechat.id as userId, wechat.nickname, wechat.phone, wechat.avatar,  ref.shop_id " +
                "FROM dub_wechat_user wechat " +
                "LEFT JOIN dub_user_shop_ref ref ON ref.user_id = wechat.id " +
                "WHERE  ";
        if (shopId > 0) {
            sql += String.format(" (ref.shop_id = %s || ref.id IS NULL) ", shopId);
        } else {
            sql += " ref.id IS NULL ";
        }
        log.info("查询用户列表，sql:{}", sql);
        List<ShopUserVo> list = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ShopUserVo.class));
        return list;
    }
}
